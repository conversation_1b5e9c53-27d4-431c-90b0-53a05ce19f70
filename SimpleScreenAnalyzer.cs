using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Text.Json;

namespace StealthScreenAnalyzer
{
    public partial class SimpleForm : Form
    {
        private HttpClient _httpClient;
        private string _apiKey = "************************************************************************************************************";
        private Button _readScreenButton;
        private Button _exitButton;
        private TextBox _responseBox;
        private Label _statusLabel;
        private bool _isAnalyzing = false;

        public SimpleForm()
        {
            InitializeComponent();
            InitializeHttpClient();
        }

        private void InitializeComponent()
        {
            this.Text = "Simple Screen Analyzer";
            this.Size = new Size(500, 400);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.BackColor = Color.White;
            this.ShowInTaskbar = true;

            // Read Screen button
            _readScreenButton = new Button()
            {
                Text = "Read Screen",
                Size = new Size(120, 40),
                Location = new Point(20, 20),
                BackColor = Color.Blue,
                ForeColor = Color.White,
                Font = new Font("Arial", 10, FontStyle.Bold)
            };
            _readScreenButton.Click += ReadScreenButton_Click;

            // Exit button
            _exitButton = new Button()
            {
                Text = "EXIT",
                Size = new Size(80, 40),
                Location = new Point(160, 20),
                BackColor = Color.Red,
                ForeColor = Color.White,
                Font = new Font("Arial", 10, FontStyle.Bold)
            };
            _exitButton.Click += (s, e) => this.Close();

            // Status label
            _statusLabel = new Label()
            {
                Text = "Ready",
                Size = new Size(200, 30),
                Location = new Point(20, 70),
                Font = new Font("Arial", 10),
                ForeColor = Color.Green
            };

            // Response box
            _responseBox = new TextBox()
            {
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                ReadOnly = true,
                Size = new Size(450, 250),
                Location = new Point(20, 110),
                Font = new Font("Arial", 9),
                BackColor = Color.LightGray
            };

            this.Controls.AddRange(new Control[] { _readScreenButton, _exitButton, _statusLabel, _responseBox });
        }

        private void InitializeHttpClient()
        {
            _httpClient = new HttpClient();
        }

        private async void ReadScreenButton_Click(object sender, EventArgs e)
        {
            if (_isAnalyzing) return;

            try
            {
                _isAnalyzing = true;
                _readScreenButton.Text = "Reading...";
                _readScreenButton.Enabled = false;
                _statusLabel.Text = "Analyzing...";
                _statusLabel.ForeColor = Color.Orange;

                // Get user question
                string question = GetUserQuestion();
                if (string.IsNullOrEmpty(question))
                {
                    ResetButton();
                    return;
                }

                // Capture screen
                string base64Image = CaptureScreen();

                // Send to Claude API
                string response = await SendToClaudeAPI(base64Image, question);

                // Show response
                _responseBox.Text = response;
                _statusLabel.Text = "Complete";
                _statusLabel.ForeColor = Color.Green;
            }
            catch (Exception ex)
            {
                _responseBox.Text = $"Error: {ex.Message}";
                _statusLabel.Text = "Error";
                _statusLabel.ForeColor = Color.Red;
            }
            finally
            {
                ResetButton();
            }
        }

        private void ResetButton()
        {
            _isAnalyzing = false;
            _readScreenButton.Text = "Read Screen";
            _readScreenButton.Enabled = true;
        }

        private string GetUserQuestion()
        {
            string input = Microsoft.VisualBasic.Interaction.InputBox(
                "What do you want to know about the screen?",
                "Screen Analysis",
                "Describe what you see on the screen");
            return input;
        }

        private string CaptureScreen()
        {
            Rectangle bounds = Screen.PrimaryScreen.Bounds;
            using (Bitmap bitmap = new Bitmap(bounds.Width, bounds.Height))
            {
                using (Graphics g = Graphics.FromImage(bitmap))
                {
                    g.CopyFromScreen(bounds.X, bounds.Y, 0, 0, bounds.Size, CopyPixelOperation.SourceCopy);
                }

                using (MemoryStream ms = new MemoryStream())
                {
                    bitmap.Save(ms, ImageFormat.Png);
                    return Convert.ToBase64String(ms.ToArray());
                }
            }
        }

        private async Task<string> SendToClaudeAPI(string base64Image, string question)
        {
            try
            {
                var requestBody = new
                {
                    model = "claude-3-5-sonnet-20241022",
                    max_tokens = 1000,
                    messages = new[]
                    {
                        new
                        {
                            role = "user",
                            content = new object[]
                            {
                                new { type = "text", text = question },
                                new
                                {
                                    type = "image",
                                    source = new
                                    {
                                        type = "base64",
                                        media_type = "image/png",
                                        data = base64Image
                                    }
                                }
                            }
                        }
                    }
                };

                var json = JsonSerializer.Serialize(requestBody);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Add("x-api-key", _apiKey);
                _httpClient.DefaultRequestHeaders.Add("anthropic-version", "2023-06-01");

                var response = await _httpClient.PostAsync("https://api.anthropic.com/v1/messages", content);
                var responseString = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var jsonResponse = JsonSerializer.Deserialize<JsonElement>(responseString);
                    return jsonResponse.GetProperty("content")[0].GetProperty("text").GetString() ?? "No response";
                }
                else
                {
                    return $"API Error: {responseString}";
                }
            }
            catch (Exception ex)
            {
                return $"Connection Error: {ex.Message}";
            }
        }
    }

    static class Program
    {
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new SimpleForm());
        }
    }
}
