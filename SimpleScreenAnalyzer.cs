using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Text.Json;

namespace StealthScreenAnalyzer
{
    public partial class SimpleForm : Form
    {
        private HttpClient _httpClient;
        private string _apiKey = "************************************************************************************************************";
        private Button _readScreenButton;
        private Button _exitButton;
        private Button _transparentButton;
        private TextBox _responseBox;
        private Label _statusLabel;
        private Panel _controlPanel;
        private Panel _readingPanel;
        private bool _isAnalyzing = false;
        private bool _isTransparent = false;

        public SimpleForm()
        {
            InitializeComponent();
            InitializeHttpClient();

            // Handle window resize to maintain panel proportions
            this.Resize += OnFormResize;
        }

        private void OnFormResize(object sender, EventArgs e)
        {
            if (_controlPanel != null)
            {
                _controlPanel.Width = (int)(this.ClientSize.Width * 0.4);
            }
        }

        private void InitializeComponent()
        {
            this.Text = "Screen Analyzer - Split Panel Mode";
            this.Size = new Size(1000, 600); // Much larger default size
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.Sizable; // Allow resizing
            this.MaximizeBox = true; // Enable maximize
            this.MinimizeBox = true; // Enable minimize
            this.BackColor = Color.DarkGray;
            this.ShowInTaskbar = true;
            this.WindowState = FormWindowState.Normal;

            // Create split panels
            SetupSplitPanels();
        }

        private void SetupSplitPanels()
        {
            // Left Control Panel (40% of width)
            _controlPanel = new Panel()
            {
                Dock = DockStyle.Left,
                Width = (int)(this.ClientSize.Width * 0.4),
                BackColor = Color.FromArgb(240, 240, 240),
                BorderStyle = BorderStyle.FixedSingle
            };

            // Right Reading Panel (60% of width)
            _readingPanel = new Panel()
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(250, 250, 250),
                BorderStyle = BorderStyle.FixedSingle
            };

            // Add panels to form
            this.Controls.Add(_readingPanel);
            this.Controls.Add(_controlPanel);

            // Setup controls in left panel
            SetupControlPanelControls();

            // Setup reading area in right panel
            SetupReadingPanelControls();
        }

        private void SetupControlPanelControls()
        {
            // Read Screen button
            _readScreenButton = new Button()
            {
                Text = "📸 Read Screen",
                Size = new Size(150, 50),
                Location = new Point(20, 20),
                BackColor = Color.Blue,
                ForeColor = Color.White,
                Font = new Font("Arial", 11, FontStyle.Bold)
            };
            _readScreenButton.Click += ReadScreenButton_Click;

            // Exit button
            _exitButton = new Button()
            {
                Text = "❌ EXIT",
                Size = new Size(100, 40),
                Location = new Point(20, 80),
                BackColor = Color.Red,
                ForeColor = Color.White,
                Font = new Font("Arial", 10, FontStyle.Bold)
            };
            _exitButton.Click += (s, e) => this.Close();

            // Stealth mode button
            _transparentButton = new Button()
            {
                Text = "🥷 Go Stealth",
                Size = new Size(150, 40),
                Location = new Point(20, 130),
                BackColor = Color.Purple,
                ForeColor = Color.White,
                Font = new Font("Arial", 10, FontStyle.Bold)
            };
            _transparentButton.Click += ToggleTransparentMode;

            // Status label
            _statusLabel = new Label()
            {
                Text = "Ready",
                Size = new Size(200, 60),
                Location = new Point(20, 180),
                Font = new Font("Arial", 12, FontStyle.Bold),
                ForeColor = Color.Green,
                TextAlign = ContentAlignment.MiddleLeft
            };

            // Add controls to control panel
            _controlPanel.Controls.AddRange(new Control[] {
                _readScreenButton, _exitButton, _transparentButton, _statusLabel
            });
        }

        private void SetupReadingPanelControls()
        {
            // Response box fills the entire reading panel
            _responseBox = new TextBox()
            {
                Dock = DockStyle.Fill,
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                ReadOnly = true,
                Font = new Font("Arial", 11),
                BackColor = Color.White,
                ForeColor = Color.Black,
                BorderStyle = BorderStyle.None,
                Margin = new Padding(10)
            };

            _readingPanel.Controls.Add(_responseBox);
        }

        private void InitializeHttpClient()
        {
            _httpClient = new HttpClient();
        }

        private async void ReadScreenButton_Click(object sender, EventArgs e)
        {
            if (_isAnalyzing) return;

            try
            {
                _isAnalyzing = true;
                _readScreenButton.Text = "Reading...";
                _readScreenButton.Enabled = false;
                _statusLabel.Text = "Capturing screen...";
                _statusLabel.ForeColor = Color.Orange;

                // Capture screen
                string base64Image = CaptureScreen();

                _statusLabel.Text = "Analyzing with AI...";

                // Smart question answering prompt
                string question = @"You are an expert assistant. When you see a question:

1. If it's MULTIPLE CHOICE - Start your answer with: 'ANSWER: [Letter(s)]' then explain why
2. If it's a technical question - Provide the direct solution
3. If it's code - Fix it or explain what it does
4. If it's a problem - Give step-by-step solution

CRITICAL: For multiple choice questions, ALWAYS start with 'ANSWER: A' or 'ANSWER: B and D' etc.

Example format:
ANSWER: A and C
Explanation: Option A is correct because... Option C is correct because...

Don't just explain concepts - give the specific answer first, then explain.";

                // Send to Claude API
                string response = await SendToClaudeAPI(base64Image, question);

                // Show response
                _responseBox.Text = response;
                _statusLabel.Text = "Analysis Complete";
                _statusLabel.ForeColor = Color.Green;
            }
            catch (Exception ex)
            {
                _responseBox.Text = $"Error: {ex.Message}";
                _statusLabel.Text = "Error";
                _statusLabel.ForeColor = Color.Red;
            }
            finally
            {
                ResetButton();
            }
        }

        private void ResetButton()
        {
            _isAnalyzing = false;
            _readScreenButton.Text = "Read Screen";
            _readScreenButton.Enabled = true;
        }

        private void ToggleTransparentMode(object sender, EventArgs e)
        {
            if (!_isTransparent)
            {
                // Go stealth mode - simple transparency approach
                this.TopMost = true;
                this.ShowInTaskbar = false;
                _transparentButton.Text = "🔍 Go Normal";
                _isTransparent = true;
                _statusLabel.Text = "STEALTH MODE\nActive";
                _statusLabel.ForeColor = Color.Red;

                // Make the whole window transparent (65% opacity)
                this.Opacity = 0.65;

                // Darken the control panel slightly for better contrast
                _controlPanel.BackColor = Color.FromArgb(220, 220, 220);

                // Keep reading panel light for better text visibility
                _readingPanel.BackColor = Color.FromArgb(245, 245, 245);
                _responseBox.BackColor = Color.White;
            }
            else
            {
                // Go normal mode
                this.Opacity = 1.0; // Fully visible
                this.TopMost = false;
                this.ShowInTaskbar = true;
                _transparentButton.Text = "🥷 Go Stealth";
                _isTransparent = false;
                _statusLabel.Text = "Normal Mode";
                _statusLabel.ForeColor = Color.Green;

                // Reset panel colors to normal
                _controlPanel.BackColor = Color.FromArgb(240, 240, 240);
                _readingPanel.BackColor = Color.FromArgb(250, 250, 250);
                _responseBox.BackColor = Color.White;
            }
        }



        private string CaptureScreen()
        {
            Rectangle bounds = Screen.PrimaryScreen.Bounds;
            using (Bitmap bitmap = new Bitmap(bounds.Width, bounds.Height))
            {
                using (Graphics g = Graphics.FromImage(bitmap))
                {
                    g.CopyFromScreen(bounds.X, bounds.Y, 0, 0, bounds.Size, CopyPixelOperation.SourceCopy);
                }

                using (MemoryStream ms = new MemoryStream())
                {
                    bitmap.Save(ms, ImageFormat.Png);
                    return Convert.ToBase64String(ms.ToArray());
                }
            }
        }

        private async Task<string> SendToClaudeAPI(string base64Image, string question)
        {
            try
            {
                var requestBody = new
                {
                    model = "claude-3-5-sonnet-20241022",
                    max_tokens = 1000,
                    messages = new[]
                    {
                        new
                        {
                            role = "user",
                            content = new object[]
                            {
                                new { type = "text", text = question },
                                new
                                {
                                    type = "image",
                                    source = new
                                    {
                                        type = "base64",
                                        media_type = "image/png",
                                        data = base64Image
                                    }
                                }
                            }
                        }
                    }
                };

                var json = JsonSerializer.Serialize(requestBody);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Add("x-api-key", _apiKey);
                _httpClient.DefaultRequestHeaders.Add("anthropic-version", "2023-06-01");

                var response = await _httpClient.PostAsync("https://api.anthropic.com/v1/messages", content);
                var responseString = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var jsonResponse = JsonSerializer.Deserialize<JsonElement>(responseString);
                    return jsonResponse.GetProperty("content")[0].GetProperty("text").GetString() ?? "No response";
                }
                else
                {
                    return $"API Error: {responseString}";
                }
            }
            catch (Exception ex)
            {
                return $"Connection Error: {ex.Message}";
            }
        }
    }

    static class Program
    {
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new SimpleForm());
        }
    }
}
