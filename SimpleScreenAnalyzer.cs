using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Text.Json;

namespace StealthScreenAnalyzer
{
    public partial class SimpleForm : Form
    {
        private HttpClient _httpClient;
        private string _apiKey = "************************************************************************************************************";
        private Button _readScreenButton;
        private Button _exitButton;
        private Button _transparentButton;
        private TextBox _responseBox;
        private Label _statusLabel;
        private bool _isAnalyzing = false;
        private bool _isTransparent = false;

        public SimpleForm()
        {
            InitializeComponent();
            InitializeHttpClient();
        }

        private void InitializeComponent()
        {
            this.Text = "Simple Screen Analyzer";
            this.Size = new Size(600, 450); // Wider for better reading
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.BackColor = Color.White;
            this.ShowInTaskbar = true;

            // Read Screen button
            _readScreenButton = new Button()
            {
                Text = "Read Screen",
                Size = new Size(120, 40),
                Location = new Point(20, 20),
                BackColor = Color.Blue,
                ForeColor = Color.White,
                Font = new Font("Arial", 10, FontStyle.Bold)
            };
            _readScreenButton.Click += ReadScreenButton_Click;

            // Exit button
            _exitButton = new Button()
            {
                Text = "EXIT",
                Size = new Size(80, 40),
                Location = new Point(160, 20),
                BackColor = Color.Red,
                ForeColor = Color.White,
                Font = new Font("Arial", 10, FontStyle.Bold)
            };
            _exitButton.Click += (s, e) => this.Close();

            // Transparent mode button
            _transparentButton = new Button()
            {
                Text = "Go Stealth",
                Size = new Size(100, 40),
                Location = new Point(260, 20),
                BackColor = Color.Purple,
                ForeColor = Color.White,
                Font = new Font("Arial", 9, FontStyle.Bold)
            };
            _transparentButton.Click += ToggleTransparentMode;

            // Status label
            _statusLabel = new Label()
            {
                Text = "Ready",
                Size = new Size(200, 30),
                Location = new Point(20, 70),
                Font = new Font("Arial", 10),
                ForeColor = Color.Green
            };

            // Response box
            _responseBox = new TextBox()
            {
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                ReadOnly = true,
                Size = new Size(550, 280), // Wider and taller for better reading
                Location = new Point(20, 110),
                Font = new Font("Arial", 10), // Slightly larger font
                BackColor = Color.LightGray
            };

            this.Controls.AddRange(new Control[] { _readScreenButton, _exitButton, _transparentButton, _statusLabel, _responseBox });
        }

        private void InitializeHttpClient()
        {
            _httpClient = new HttpClient();
        }

        private async void ReadScreenButton_Click(object sender, EventArgs e)
        {
            if (_isAnalyzing) return;

            try
            {
                _isAnalyzing = true;
                _readScreenButton.Text = "Reading...";
                _readScreenButton.Enabled = false;
                _statusLabel.Text = "Capturing screen...";
                _statusLabel.ForeColor = Color.Orange;

                // Capture screen
                string base64Image = CaptureScreen();

                _statusLabel.Text = "Analyzing with AI...";

                // Automatically analyze the screen with a default question
                string question = "Please describe what you see on this screen in detail. Include any text, applications, windows, or content that is visible.";

                // Send to Claude API
                string response = await SendToClaudeAPI(base64Image, question);

                // Show response
                _responseBox.Text = response;
                _statusLabel.Text = "Analysis Complete";
                _statusLabel.ForeColor = Color.Green;
            }
            catch (Exception ex)
            {
                _responseBox.Text = $"Error: {ex.Message}";
                _statusLabel.Text = "Error";
                _statusLabel.ForeColor = Color.Red;
            }
            finally
            {
                ResetButton();
            }
        }

        private void ResetButton()
        {
            _isAnalyzing = false;
            _readScreenButton.Text = "Read Screen";
            _readScreenButton.Enabled = true;
        }

        private void ToggleTransparentMode(object sender, EventArgs e)
        {
            if (!_isTransparent)
            {
                // Go stealth mode - much less transparent
                this.Opacity = 0.7; // More visible in stealth mode
                this.TopMost = true;
                this.ShowInTaskbar = false;
                _transparentButton.Text = "Go Normal";
                _isTransparent = true;
                _statusLabel.Text = "Stealth Mode";
                _statusLabel.ForeColor = Color.Red;

                // Make response box more readable in stealth mode
                _responseBox.BackColor = Color.FromArgb(240, 240, 240);
                _responseBox.ForeColor = Color.Black;
            }
            else
            {
                // Go normal mode
                this.Opacity = 1.0; // Fully visible
                this.TopMost = false;
                this.ShowInTaskbar = true;
                _transparentButton.Text = "Go Stealth";
                _isTransparent = false;
                _statusLabel.Text = "Normal Mode";
                _statusLabel.ForeColor = Color.Green;

                // Reset response box colors
                _responseBox.BackColor = Color.LightGray;
                _responseBox.ForeColor = Color.Black;
            }
        }



        private string CaptureScreen()
        {
            Rectangle bounds = Screen.PrimaryScreen.Bounds;
            using (Bitmap bitmap = new Bitmap(bounds.Width, bounds.Height))
            {
                using (Graphics g = Graphics.FromImage(bitmap))
                {
                    g.CopyFromScreen(bounds.X, bounds.Y, 0, 0, bounds.Size, CopyPixelOperation.SourceCopy);
                }

                using (MemoryStream ms = new MemoryStream())
                {
                    bitmap.Save(ms, ImageFormat.Png);
                    return Convert.ToBase64String(ms.ToArray());
                }
            }
        }

        private async Task<string> SendToClaudeAPI(string base64Image, string question)
        {
            try
            {
                var requestBody = new
                {
                    model = "claude-3-5-sonnet-20241022",
                    max_tokens = 1000,
                    messages = new[]
                    {
                        new
                        {
                            role = "user",
                            content = new object[]
                            {
                                new { type = "text", text = question },
                                new
                                {
                                    type = "image",
                                    source = new
                                    {
                                        type = "base64",
                                        media_type = "image/png",
                                        data = base64Image
                                    }
                                }
                            }
                        }
                    }
                };

                var json = JsonSerializer.Serialize(requestBody);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Add("x-api-key", _apiKey);
                _httpClient.DefaultRequestHeaders.Add("anthropic-version", "2023-06-01");

                var response = await _httpClient.PostAsync("https://api.anthropic.com/v1/messages", content);
                var responseString = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var jsonResponse = JsonSerializer.Deserialize<JsonElement>(responseString);
                    return jsonResponse.GetProperty("content")[0].GetProperty("text").GetString() ?? "No response";
                }
                else
                {
                    return $"API Error: {responseString}";
                }
            }
            catch (Exception ex)
            {
                return $"Connection Error: {ex.Message}";
            }
        }
    }

    static class Program
    {
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new SimpleForm());
        }
    }
}
