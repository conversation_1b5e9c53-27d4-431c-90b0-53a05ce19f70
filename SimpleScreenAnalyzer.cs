using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Net.Http;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Text.Json;

namespace StealthScreenAnalyzer
{
    public partial class SimpleForm : Form
    {
        // Windows API declarations for screen capture exclusion
        [DllImport("user32.dll")]
        private static extern int SetWindowDisplayAffinity(IntPtr hwnd, uint dwAffinity);

        // Windows API declarations for global hotkeys
        [DllImport("user32.dll")]
        private static extern bool RegisterHotKey(IntPtr hWnd, int id, uint fsModifiers, uint vk);

        [DllImport("user32.dll")]
        private static extern bool UnregisterHotKey(IntPtr hWnd, int id);

        private const uint WDA_NONE = 0x00000000;
        private const uint WDA_MONITOR = 0x00000001;
        private const uint WDA_EXCLUDEFROMCAPTURE = 0x00000011;

        // Hotkey constants
        private const int HOTKEY_ID_CAPTURE = 9000;
        private const int HOTKEY_ID_TOGGLE_STEALTH = 9001;
        private const uint MOD_CONTROL = 0x0002;
        private const uint MOD_SHIFT = 0x0004;
        private const uint VK_F9 = 0x78;  // F9 key
        private const uint VK_F10 = 0x79; // F10 key
        private HttpClient _httpClient;
        private string _apiKey = "************************************************************************************************************";
        private Button _readScreenButton;
        private Button _exitButton;
        private Button _transparentButton;
        private TextBox _responseBox;
        private Label _statusLabel;
        private Panel _controlPanel;
        private Panel _readingPanel;
        private ComboBox _modelSelector;
        private Label _modelLabel;
        private MenuStrip _menuStrip;
        private ToolStripMenuItem _optionsMenu;
        private ToolStripMenuItem _normalModeMenuItem;
        private bool _isAnalyzing = false;
        private bool _isTransparent = true; // Start in stealth mode by default

        public SimpleForm()
        {
            InitializeComponent();
            InitializeHttpClient();
            RegisterGlobalHotkeys();
            SetupMenu();

            // Handle window resize to maintain panel proportions
            this.Resize += OnFormResize;

            // Start in stealth mode automatically
            this.Load += (s, e) => ActivateStealthMode();
        }

        private void RegisterGlobalHotkeys()
        {
            // Register Ctrl+Shift+F9 for screen capture
            RegisterHotKey(this.Handle, HOTKEY_ID_CAPTURE, MOD_CONTROL | MOD_SHIFT, VK_F9);

            // Register Ctrl+Shift+F10 for toggle stealth mode
            RegisterHotKey(this.Handle, HOTKEY_ID_TOGGLE_STEALTH, MOD_CONTROL | MOD_SHIFT, VK_F10);
        }

        protected override void WndProc(ref Message m)
        {
            const int WM_HOTKEY = 0x0312;

            if (m.Msg == WM_HOTKEY)
            {
                int id = m.WParam.ToInt32();

                if (id == HOTKEY_ID_CAPTURE)
                {
                    // Trigger screen capture invisibly
                    _ = Task.Run(async () => await InvisibleScreenCapture());
                }
                else if (id == HOTKEY_ID_TOGGLE_STEALTH)
                {
                    // Toggle stealth mode invisibly
                    this.Invoke(new Action(() => ToggleTransparentMode(null, null)));
                }
            }

            base.WndProc(ref m);
        }

        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            // Unregister hotkeys when form closes
            UnregisterHotKey(this.Handle, HOTKEY_ID_CAPTURE);
            UnregisterHotKey(this.Handle, HOTKEY_ID_TOGGLE_STEALTH);
            base.OnFormClosed(e);
        }

        private void SetupMenu()
        {
            _menuStrip = new MenuStrip();

            _optionsMenu = new ToolStripMenuItem("Options");
            _normalModeMenuItem = new ToolStripMenuItem("Switch to Normal Mode");
            _normalModeMenuItem.Click += (s, e) => ToggleTransparentMode(null, null);

            _optionsMenu.DropDownItems.Add(_normalModeMenuItem);
            _menuStrip.Items.Add(_optionsMenu);

            this.MainMenuStrip = _menuStrip;
            this.Controls.Add(_menuStrip);
        }

        private void ActivateStealthMode()
        {
            // Activate stealth mode immediately
            this.TopMost = true;
            this.ShowInTaskbar = false;

            // Make window invisible to screen capture/recording
            SetWindowDisplayAffinity(this.Handle, WDA_EXCLUDEFROMCAPTURE);

            // Keep window visible to user but with some transparency for discretion
            this.Opacity = 0.8;

            // Update status
            _statusLabel.Text = "TRUE STEALTH MODE\nInvisible to Recording\n\nHotkeys:\nCtrl+Shift+F9 = Capture\nCtrl+Shift+F10 = Toggle";
            _statusLabel.ForeColor = Color.Red;

            // Update menu
            _normalModeMenuItem.Text = "Switch to Normal Mode";
        }

        private void OnFormResize(object sender, EventArgs e)
        {
            if (_controlPanel != null)
            {
                _controlPanel.Width = (int)(this.ClientSize.Width * 0.4);
            }
        }

        private void InitializeComponent()
        {
            this.Text = "Screen Analyzer - Split Panel Mode";
            this.Size = new Size(1000, 600); // Much larger default size
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.Sizable; // Allow resizing
            this.MaximizeBox = true; // Enable maximize
            this.MinimizeBox = true; // Enable minimize
            this.BackColor = Color.DarkGray;
            this.ShowInTaskbar = true;
            this.WindowState = FormWindowState.Normal;

            // Create split panels
            SetupSplitPanels();
        }

        private void SetupSplitPanels()
        {
            // Left Control Panel (40% of width)
            _controlPanel = new Panel()
            {
                Dock = DockStyle.Left,
                Width = (int)(this.ClientSize.Width * 0.4),
                BackColor = Color.FromArgb(240, 240, 240),
                BorderStyle = BorderStyle.FixedSingle
            };

            // Right Reading Panel (60% of width)
            _readingPanel = new Panel()
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(250, 250, 250),
                BorderStyle = BorderStyle.FixedSingle
            };

            // Add panels to form
            this.Controls.Add(_readingPanel);
            this.Controls.Add(_controlPanel);

            // Setup controls in left panel
            SetupControlPanelControls();

            // Setup reading area in right panel
            SetupReadingPanelControls();
        }

        private void SetupControlPanelControls()
        {
            // Model selection label
            _modelLabel = new Label()
            {
                Text = "AI Model:",
                Size = new Size(150, 25),
                Location = new Point(20, 20),
                ForeColor = Color.Black,
                Font = new Font("Arial", 10, FontStyle.Bold)
            };

            // Model selector dropdown
            _modelSelector = new ComboBox()
            {
                Size = new Size(200, 30),
                Location = new Point(20, 45),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Arial", 10)
            };
            _modelSelector.Items.AddRange(new string[]
            {
                "Claude Sonnet 4",
                "ChatGPT 4o",
                "Gemini 2.5 Pro"
            });
            _modelSelector.SelectedIndex = 0; // Default to Claude

            // Read Screen button
            _readScreenButton = new Button()
            {
                Text = "📸 Read Screen",
                Size = new Size(150, 50),
                Location = new Point(20, 85),
                BackColor = Color.Blue,
                ForeColor = Color.White,
                Font = new Font("Arial", 11, FontStyle.Bold)
            };
            _readScreenButton.Click += ReadScreenButton_Click;

            // Exit button
            _exitButton = new Button()
            {
                Text = "❌ EXIT",
                Size = new Size(100, 40),
                Location = new Point(20, 145),
                BackColor = Color.Red,
                ForeColor = Color.White,
                Font = new Font("Arial", 10, FontStyle.Bold)
            };
            _exitButton.Click += (s, e) => this.Close();

            // Note: Stealth button removed - now in Options menu

            // Status label
            _statusLabel = new Label()
            {
                Text = "Ready\n\nHotkeys:\nCtrl+Shift+F9 = Capture\nCtrl+Shift+F10 = Toggle",
                Size = new Size(250, 120),
                Location = new Point(20, 195),
                Font = new Font("Arial", 10, FontStyle.Bold),
                ForeColor = Color.Green,
                TextAlign = ContentAlignment.TopLeft
            };

            // Add controls to control panel (stealth button removed)
            _controlPanel.Controls.AddRange(new Control[] {
                _modelLabel, _modelSelector, _readScreenButton, _exitButton, _statusLabel
            });
        }

        private void SetupReadingPanelControls()
        {
            // Response box fills the entire reading panel
            _responseBox = new TextBox()
            {
                Dock = DockStyle.Fill,
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                ReadOnly = true,
                Font = new Font("Arial", 11),
                BackColor = Color.White,
                ForeColor = Color.Black,
                BorderStyle = BorderStyle.None,
                Margin = new Padding(10)
            };

            _readingPanel.Controls.Add(_responseBox);
        }

        private void InitializeHttpClient()
        {
            _httpClient = new HttpClient();
        }

        private async void ReadScreenButton_Click(object sender, EventArgs e)
        {
            if (_isAnalyzing) return;

            try
            {
                _isAnalyzing = true;
                _readScreenButton.Text = "Reading...";
                _readScreenButton.Enabled = false;
                _statusLabel.Text = "Capturing screen...";
                _statusLabel.ForeColor = Color.Orange;

                // Capture screen
                string base64Image = CaptureScreen();

                _statusLabel.Text = "Analyzing with AI...";

                // Smart question answering prompt
                string question = @"You are an expert assistant. When you see a question:

1. If it's MULTIPLE CHOICE - Start with: 'ANSWER: [Letter(s)]' then give a BRIEF explanation (1-2 sentences max)
2. If it's a technical question - Provide the direct solution concisely
3. If it's code - Fix it or explain briefly what it does
4. If it's a problem - Give concise step-by-step solution

CRITICAL:
- ALWAYS start with 'ANSWER: A' or 'ANSWER: B and D' etc.
- Keep explanations SHORT and TO THE POINT
- No lengthy descriptions - just the key reason why

Example format:
ANSWER: A and C
A: Creates private endpoint for secure access. C: Maps DNS name to private IP.";

                // Send to selected AI model
                string response = await SendToSelectedAPI(base64Image, question);

                // Show response
                _responseBox.Text = response;
                _statusLabel.Text = "Analysis Complete";
                _statusLabel.ForeColor = Color.Green;
            }
            catch (Exception ex)
            {
                _responseBox.Text = $"Error: {ex.Message}";
                _statusLabel.Text = "Error";
                _statusLabel.ForeColor = Color.Red;
            }
            finally
            {
                ResetButton();
            }
        }

        private void ResetButton()
        {
            _isAnalyzing = false;
            _readScreenButton.Text = "Read Screen";
            _readScreenButton.Enabled = true;
        }

        private async Task InvisibleScreenCapture()
        {
            if (_isAnalyzing) return;

            try
            {
                _isAnalyzing = true;

                // Update UI on main thread
                this.Invoke(new Action(() => {
                    _readScreenButton.Text = "Reading...";
                    _readScreenButton.Enabled = false;
                    _statusLabel.Text = "Capturing screen...";
                    _statusLabel.ForeColor = Color.Orange;
                }));

                // Capture screen
                string base64Image = CaptureScreen();

                this.Invoke(new Action(() => {
                    _statusLabel.Text = "Analyzing with AI...";
                }));

                // Smart question answering prompt
                string question = @"You are an expert assistant. When you see a question:

1. If it's MULTIPLE CHOICE - Start with: 'ANSWER: [Letter(s)]' then give a BRIEF explanation (1-2 sentences max)
2. If it's a technical question - Provide the direct solution concisely
3. If it's code - Fix it or explain briefly what it does
4. If it's a problem - Give concise step-by-step solution

CRITICAL:
- ALWAYS start with 'ANSWER: A' or 'ANSWER: B and D' etc.
- Keep explanations SHORT and TO THE POINT
- No lengthy descriptions - just the key reason why

Example format:
ANSWER: A and C
A: Creates private endpoint for secure access. C: Maps DNS name to private IP.";

                // Send to selected AI model
                string response = await SendToSelectedAPI(base64Image, question);

                // Show response on main thread
                this.Invoke(new Action(() => {
                    _responseBox.Text = response;
                    _statusLabel.Text = "Analysis Complete";
                    _statusLabel.ForeColor = Color.Green;
                }));
            }
            catch (Exception ex)
            {
                this.Invoke(new Action(() => {
                    _responseBox.Text = $"Error: {ex.Message}";
                    _statusLabel.Text = "Error";
                    _statusLabel.ForeColor = Color.Red;
                }));
            }
            finally
            {
                this.Invoke(new Action(() => ResetButton()));
            }
        }

        private void ToggleTransparentMode(object sender, EventArgs e)
        {
            if (!_isTransparent)
            {
                // Go TRUE stealth mode - invisible to screen recording/sharing
                this.TopMost = true;
                this.ShowInTaskbar = false;
                _isTransparent = true;
                _statusLabel.Text = "TRUE STEALTH MODE\nInvisible to Recording\n\nHotkeys:\nCtrl+Shift+F9 = Capture\nCtrl+Shift+F10 = Toggle";
                _statusLabel.ForeColor = Color.Red;

                // Make window invisible to screen capture/recording
                SetWindowDisplayAffinity(this.Handle, WDA_EXCLUDEFROMCAPTURE);

                // Keep window visible to user but with some transparency for discretion
                this.Opacity = 0.8;

                // Darken the control panel slightly for better contrast
                _controlPanel.BackColor = Color.FromArgb(220, 220, 220);

                // Keep reading panel light for better text visibility
                _readingPanel.BackColor = Color.FromArgb(245, 245, 245);
                _responseBox.BackColor = Color.White;

                // Update menu
                _normalModeMenuItem.Text = "Switch to Normal Mode";
            }
            else
            {
                // Go normal mode - visible to screen recording again
                this.Opacity = 1.0; // Fully visible
                this.TopMost = false;
                this.ShowInTaskbar = true;
                _isTransparent = false;
                _statusLabel.Text = "Normal Mode\n\nHotkeys:\nCtrl+Shift+F9 = Capture\nCtrl+Shift+F10 = Toggle";
                _statusLabel.ForeColor = Color.Green;

                // Make window visible to screen capture again
                SetWindowDisplayAffinity(this.Handle, WDA_NONE);

                // Reset panel colors to normal
                _controlPanel.BackColor = Color.FromArgb(240, 240, 240);
                _readingPanel.BackColor = Color.FromArgb(250, 250, 250);
                _responseBox.BackColor = Color.White;

                // Update menu
                _normalModeMenuItem.Text = "Switch to Stealth Mode";
            }
        }



        private string CaptureScreen()
        {
            Rectangle bounds = Screen.PrimaryScreen.Bounds;
            using (Bitmap bitmap = new Bitmap(bounds.Width, bounds.Height))
            {
                using (Graphics g = Graphics.FromImage(bitmap))
                {
                    g.CopyFromScreen(bounds.X, bounds.Y, 0, 0, bounds.Size, CopyPixelOperation.SourceCopy);
                }

                using (MemoryStream ms = new MemoryStream())
                {
                    bitmap.Save(ms, ImageFormat.Png);
                    return Convert.ToBase64String(ms.ToArray());
                }
            }
        }

        private async Task<string> SendToSelectedAPI(string base64Image, string question)
        {
            string selectedModel = _modelSelector.SelectedItem?.ToString() ?? "Claude Sonnet 4";

            switch (selectedModel)
            {
                case "Claude Sonnet 4":
                    return await SendToClaudeAPI(base64Image, question);
                case "ChatGPT 4o":
                    return await SendToChatGPTAPI(base64Image, question);
                case "Gemini 2.5 Pro":
                    return await SendToGeminiAPI(base64Image, question);
                default:
                    return await SendToClaudeAPI(base64Image, question);
            }
        }

        private async Task<string> SendToClaudeAPI(string base64Image, string question)
        {
            try
            {
                var requestBody = new
                {
                    model = "claude-sonnet-4-20250514", // Latest Claude Sonnet 4
                    max_tokens = 1000,
                    messages = new[]
                    {
                        new
                        {
                            role = "user",
                            content = new object[]
                            {
                                new { type = "text", text = question },
                                new
                                {
                                    type = "image",
                                    source = new
                                    {
                                        type = "base64",
                                        media_type = "image/png",
                                        data = base64Image
                                    }
                                }
                            }
                        }
                    }
                };

                var json = JsonSerializer.Serialize(requestBody);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Add("x-api-key", "************************************************************************************************************");
                _httpClient.DefaultRequestHeaders.Add("anthropic-version", "2023-06-01");

                var response = await _httpClient.PostAsync("https://api.anthropic.com/v1/messages", content);
                var responseString = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var jsonResponse = JsonSerializer.Deserialize<JsonElement>(responseString);
                    return jsonResponse.GetProperty("content")[0].GetProperty("text").GetString() ?? "No response";
                }
                else
                {
                    return $"API Error: {responseString}";
                }
            }
            catch (Exception ex)
            {
                return $"Connection Error: {ex.Message}";
            }
        }

        private async Task<string> SendToChatGPTAPI(string base64Image, string question)
        {
            try
            {
                var requestBody = new
                {
                    model = "gpt-4o",
                    max_tokens = 1000,
                    messages = new[]
                    {
                        new
                        {
                            role = "user",
                            content = new object[]
                            {
                                new
                                {
                                    type = "text",
                                    text = question
                                },
                                new
                                {
                                    type = "image_url",
                                    image_url = new
                                    {
                                        url = $"data:image/png;base64,{base64Image}"
                                    }
                                }
                            }
                        }
                    }
                };

                var json = JsonSerializer.Serialize(requestBody);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Add("Authorization", "Bearer ********************************************************************************************************************************************************************");

                var response = await _httpClient.PostAsync("https://api.openai.com/v1/chat/completions", content);
                var responseString = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var jsonResponse = JsonSerializer.Deserialize<JsonElement>(responseString);
                    return jsonResponse.GetProperty("choices")[0]
                        .GetProperty("message")
                        .GetProperty("content").GetString() ?? "No response";
                }
                else
                {
                    return $"API Error: {responseString}";
                }
            }
            catch (Exception ex)
            {
                return $"Connection Error: {ex.Message}";
            }
        }

        private async Task<string> SendToGeminiAPI(string base64Image, string question)
        {
            try
            {
                var requestBody = new
                {
                    contents = new[]
                    {
                        new
                        {
                            parts = new object[]
                            {
                                new
                                {
                                    text = question
                                },
                                new
                                {
                                    inline_data = new
                                    {
                                        mime_type = "image/png",
                                        data = base64Image
                                    }
                                }
                            }
                        }
                    },
                    generationConfig = new
                    {
                        maxOutputTokens = 4000,
                        temperature = 0.1
                    },
                    systemInstruction = new
                    {
                        parts = new[]
                        {
                            new
                            {
                                text = "Respond directly without showing your thinking process. Be VERY concise - use 1-2 sentences max for explanations. No lengthy descriptions."
                            }
                        }
                    }
                };

                var json = JsonSerializer.Serialize(requestBody);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                _httpClient.DefaultRequestHeaders.Clear();

                var apiKey = "AIzaSyAwRbMjsRmhb2ObWGSywk_c1BUtw_zGRSQ";
                var response = await _httpClient.PostAsync($"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-pro:generateContent?key={apiKey}", content);
                var responseString = await response.Content.ReadAsStringAsync();

                // Debug: Show raw response
                if (!response.IsSuccessStatusCode)
                {
                    return $"Gemini API Error ({response.StatusCode}): {responseString}";
                }

                // Parse and handle response more carefully
                var jsonResponse = JsonSerializer.Deserialize<JsonElement>(responseString);

                // Check if response has expected structure
                if (!jsonResponse.TryGetProperty("candidates", out var candidates))
                {
                    return $"Gemini Error: No candidates in response. Raw: {responseString}";
                }

                if (candidates.GetArrayLength() == 0)
                {
                    return "Gemini Error: No candidates returned";
                }

                var firstCandidate = candidates[0];

                // Check for finish reason
                if (firstCandidate.TryGetProperty("finishReason", out var finishReason))
                {
                    var reason = finishReason.GetString();
                    if (reason == "MAX_TOKENS")
                    {
                        return "Gemini Error: Response was cut off due to token limit. Try with a simpler question or the response was too long.";
                    }
                }

                if (!firstCandidate.TryGetProperty("content", out var content_prop))
                {
                    return $"Gemini Error: No content in candidate. Raw: {responseString}";
                }

                if (!content_prop.TryGetProperty("parts", out var parts))
                {
                    return $"Gemini Error: No parts in content. Raw: {responseString}";
                }

                if (parts.GetArrayLength() == 0)
                {
                    return "Gemini Error: No parts returned";
                }

                var firstPart = parts[0];
                if (!firstPart.TryGetProperty("text", out var textElement))
                {
                    return $"Gemini Error: No text in part. Raw: {responseString}";
                }

                return textElement.GetString() ?? "No response text";
            }
            catch (Exception ex)
            {
                return $"Gemini Connection Error: {ex.Message}";
            }
        }
    }

    static class Program
    {
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new SimpleForm());
        }
    }
}
