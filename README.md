# Stealth Screen Analyzer

A Windows Forms application that provides AI-powered screen analysis using Claude API. The application creates a transparent overlay that can capture and analyze screen content.

## Features

- **Transparent Overlay**: Nearly invisible overlay that doesn't interfere with screen sharing
- **Screen Capture**: Captures the current screen content for analysis
- **AI Analysis**: Uses Claude API to analyze screen content based on user questions
- **Stealth Mode**: Designed to be undetectable during screen sharing sessions
- **Memory Management**: Automatic memory cleanup to prevent resource leaks

## Requirements

- .NET 8.0 or later
- Windows operating system
- Claude API key from Anthropic

## Setup

1. **Build the application**:
   ```bash
   dotnet build
   ```

2. **Run the application**:
   ```bash
   dotnet run
   ```

3. **API Key Configuration**:
   - On first run, you'll be prompted to enter your Claude API key
   - The key is encrypted and stored locally in `%APPDATA%\SystemConfig\auth.dat`

## Usage

1. **Launch the application** - A small control panel will appear in the top-right corner
2. **Click "Read Screen"** - This will prompt you to enter a question about the screen
3. **Enter your question** - Ask what you want to know about the current screen content
4. **View results** - The AI analysis will be displayed in a popup window

## Controls

- **Read Screen**: Captures screen and analyzes it based on your question
- **Hide/Show**: Toggle visibility of the control panel
- **×**: Close the response window

## Technical Details

### File Structure
- `StealthScreenAnalyzer.cs` - Main application code
- `StealthScreenAnalyzer.csproj` - Project configuration
- `README.md` - This documentation

### Dependencies
- System.Drawing.Common (8.0.0) - For screen capture functionality
- System.Text.Json (8.0.5) - For API communication

### API Integration
- Uses Claude Sonnet 4 model via Anthropic API
- Sends base64-encoded PNG screenshots
- Supports custom questions for analysis

## Security Notes

- API keys are stored encrypted locally
- Application runs with minimal permissions
- Screen capture is temporary and not stored permanently
- Designed to be undetectable during screen sharing

## Building from Source

```bash
# Clone or download the source files
# Navigate to the project directory
cd StealthScreenAnalyzer

# Restore dependencies
dotnet restore

# Build the project
dotnet build

# Run the application
dotnet run
```

## Troubleshooting

### Common Issues

1. **Build Errors**: Ensure you have .NET 8.0 SDK installed
2. **API Errors**: Verify your Claude API key is valid and has sufficient credits
3. **Screen Capture Issues**: Run as administrator if screen capture fails
4. **Transparency Issues**: Some screen sharing software may still detect the overlay

### Warnings
The build may show nullable reference warnings - these are safe to ignore as they don't affect functionality.

## License

This project is for educational and personal use. Please ensure compliance with Anthropic's API terms of service.
