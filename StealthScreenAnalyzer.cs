using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Runtime.InteropServices;
using System.Diagnostics;
using System.Text.Json;
using System.Collections.Generic;
using System.Security.Cryptography;
using System.Reflection;
using System.Threading;

namespace StealthScreenAnalyzer
{
    public partial class OverlayForm : Form
    {
        private HttpClient _httpClient;
        private string _apiKey;
        private Panel _controlPanel;
        private Button _readScreenButton;
        private TextBox _responseBox;
        private Button _closeResponseButton;
        private bool _isAnalyzing = false;
        private System.Windows.Forms.Timer _memoryCleanupTimer;
        private Random _random = new Random();

        // Screen sharing stealth constants
        private const int WS_EX_LAYERED = 0x80000;
        private const int WS_EX_TRANSPARENT = 0x20;
        private const int WS_EX_NOACTIVATE = 0x8000000;
        private const int WS_EX_TOOLWINDOW = 0x80;
        private const int GWL_EXSTYLE = -20;
        private const int LWA_ALPHA = 0x2;

        public OverlayForm()
        {
            InitializeComponent();
            InitializeOverlay();
            InitializeHttpClient();
            SetupControls();
            
            // Start memory cleanup timer
            _memoryCleanupTimer = new System.Windows.Forms.Timer();
            _memoryCleanupTimer.Interval = 30000;
            _memoryCleanupTimer.Tick += CleanupMemory;
            _memoryCleanupTimer.Start();
            
            // Load API key
            _apiKey = LoadApiKey();
        }

        private void InitializeOverlay()
        {
            // Make form cover entire screen
            this.FormBorderStyle = FormBorderStyle.None;
            this.WindowState = FormWindowState.Maximized;
            this.TopMost = true;
            this.ShowInTaskbar = false;
            
            // Set background to be semi-transparent for testing
            this.BackColor = Color.FromArgb(20, 20, 20);
            // this.TransparencyKey = Color.Black; // Commented out for testing
            
            // Make the form layered and semi-transparent
            SetWindowLong(this.Handle, GWL_EXSTYLE, 
                GetWindowLong(this.Handle, GWL_EXSTYLE) | 
                WS_EX_LAYERED | WS_EX_TOOLWINDOW | WS_EX_NOACTIVATE);
            
            // Set transparency level (255 = opaque, 0 = invisible)
            SetLayeredWindowAttributes(this.Handle, 0, 255, LWA_ALPHA); // Fully visible for testing
        }

        private void SetupControls()
        {
            // Control panel (small, positioned in corner)
            _controlPanel = new Panel()
            {
                Size = new Size(250, 80),
                Location = new Point(Screen.PrimaryScreen.WorkingArea.Width - 260, 10),
                BackColor = Color.DarkBlue, // Very visible blue background for testing
                BorderStyle = BorderStyle.FixedSingle,
                Visible = true
            };

            // Read Screen button
            _readScreenButton = new Button()
            {
                Text = "Read Screen",
                Size = new Size(90, 25),
                Location = new Point(5, 5),
                BackColor = Color.FromArgb(70, 130, 180),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 8, FontStyle.Bold)
            };
            _readScreenButton.FlatAppearance.BorderSize = 0;
            _readScreenButton.Click += ReadScreenButton_Click;

            // Status label
            var statusLabel = new Label()
            {
                Text = "Ready",
                Size = new Size(90, 25),
                Location = new Point(100, 5),
                ForeColor = Color.LightGreen,
                BackColor = Color.Transparent,
                Font = new Font("Segoe UI", 8),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Hide/Show toggle
            var toggleButton = new Button()
            {
                Text = "Hide",
                Size = new Size(40, 25),
                Location = new Point(5, 30),
                BackColor = Color.FromArgb(180, 70, 70),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 7)
            };
            toggleButton.FlatAppearance.BorderSize = 0;
            toggleButton.Click += (s, e) => 
            {
                _controlPanel.Visible = !_controlPanel.Visible;
                toggleButton.Text = _controlPanel.Visible ? "Hide" : "Show";
            };

            _controlPanel.Controls.AddRange(new Control[] { _readScreenButton, statusLabel, toggleButton });
            this.Controls.Add(_controlPanel);

            // Response display box (initially hidden)
            _responseBox = new TextBox()
            {
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                ReadOnly = true,
                Size = new Size(600, 300),
                Location = new Point(50, 50),
                BackColor = Color.FromArgb(40, 40, 40),
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 9),
                Visible = false,
                BorderStyle = BorderStyle.FixedSingle
            };

            _closeResponseButton = new Button()
            {
                Text = "×",
                Size = new Size(25, 25),
                Location = new Point(_responseBox.Right - 25, _responseBox.Top),
                BackColor = Color.FromArgb(180, 70, 70),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                Visible = false
            };
            _closeResponseButton.FlatAppearance.BorderSize = 0;
            _closeResponseButton.Click += (s, e) => HideResponse();

            this.Controls.AddRange(new Control[] { _responseBox, _closeResponseButton });
        }

        private async void ReadScreenButton_Click(object sender, EventArgs e)
        {
            if (_isAnalyzing) return;

            try
            {
                _isAnalyzing = true;
                _readScreenButton.Text = "Reading...";
                _readScreenButton.Enabled = false;

                // Get user question first
                string question = await GetUserQuestion();
                if (string.IsNullOrEmpty(question))
                {
                    ResetButton();
                    return;
                }

                // Temporarily hide overlay for clean screenshot
                this.Visible = false;
                await Task.Delay(100); // Small delay to ensure UI is hidden

                // Capture screen
                string base64Image = CaptureScreen();
                
                // Show overlay again
                this.Visible = true;

                // Send to Claude API
                string response = await SendToClaudeAPI(base64Image, question);
                
                // Show response
                ShowResponse(response);
            }
            catch (Exception ex)
            {
                ShowResponse($"Error: {ex.Message}");
            }
            finally
            {
                ResetButton();
            }
        }

        private void ResetButton()
        {
            _isAnalyzing = false;
            _readScreenButton.Text = "Read Screen";
            _readScreenButton.Enabled = true;
        }

        private async Task<string> GetUserQuestion()
        {
            string question = "";
            var tcs = new TaskCompletionSource<string>();

            // Create a small input form
            var inputForm = new Form()
            {
                Text = "Screen Analysis",
                Size = new Size(400, 150),
                StartPosition = FormStartPosition.CenterScreen,
                TopMost = true,
                ShowInTaskbar = false,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false,
                BackColor = Color.FromArgb(45, 45, 45),
                ForeColor = Color.White
            };

            var label = new Label()
            {
                Text = "What do you want to know about the screen?",
                Location = new Point(10, 15),
                Size = new Size(360, 20),
                ForeColor = Color.White
            };

            var textBox = new TextBox()
            {
                Location = new Point(10, 40),
                Size = new Size(360, 20),
                BackColor = Color.FromArgb(60, 60, 60),
                ForeColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var analyzeButton = new Button()
            {
                Text = "Analyze",
                Location = new Point(10, 75),
                Size = new Size(75, 25),
                BackColor = Color.FromArgb(70, 130, 180),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            var cancelButton = new Button()
            {
                Text = "Cancel",
                Location = new Point(95, 75),
                Size = new Size(75, 25),
                BackColor = Color.FromArgb(180, 70, 70),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            analyzeButton.Click += (s, e) =>
            {
                question = textBox.Text;
                inputForm.DialogResult = DialogResult.OK;
                inputForm.Close();
            };

            cancelButton.Click += (s, e) =>
            {
                inputForm.DialogResult = DialogResult.Cancel;
                inputForm.Close();
            };

            inputForm.Controls.AddRange(new Control[] { label, textBox, analyzeButton, cancelButton });
            inputForm.AcceptButton = analyzeButton;

            return inputForm.ShowDialog() == DialogResult.OK ? question : "";
        }

        private string CaptureScreen()
        {
            Rectangle bounds = Screen.PrimaryScreen.Bounds;
            using (Bitmap bitmap = new Bitmap(bounds.Width, bounds.Height))
            {
                using (Graphics g = Graphics.FromImage(bitmap))
                {
                    g.CopyFromScreen(bounds.X, bounds.Y, 0, 0, bounds.Size, CopyPixelOperation.SourceCopy);
                }

                using (MemoryStream ms = new MemoryStream())
                {
                    bitmap.Save(ms, ImageFormat.Png);
                    return Convert.ToBase64String(ms.ToArray());
                }
            }
        }

        private void ShowResponse(string response)
        {
            _responseBox.Text = response;
            _responseBox.Visible = true;
            _closeResponseButton.Visible = true;

            // Position response box in center of screen
            var centerX = (Screen.PrimaryScreen.WorkingArea.Width - _responseBox.Width) / 2;
            var centerY = (Screen.PrimaryScreen.WorkingArea.Height - _responseBox.Height) / 2;
            _responseBox.Location = new Point(centerX, centerY);
            _closeResponseButton.Location = new Point(_responseBox.Right - 25, _responseBox.Top);
        }

        private void HideResponse()
        {
            _responseBox.Visible = false;
            _closeResponseButton.Visible = false;
        }

        private void InitializeHttpClient()
        {
            _httpClient = new HttpClient();
            _httpClient.DefaultRequestHeaders.Add("User-Agent",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
        }

        private string LoadApiKey()
        {
            // Hardcoded API key - replace with your actual key
            return "************************************************************************************************************";
        }

        private string PromptForApiKey()
        {
            using (var form = new Form())
            {
                form.Text = "Configuration";
                form.Size = new Size(400, 150);
                form.StartPosition = FormStartPosition.CenterScreen;
                form.TopMost = true;
                form.BackColor = Color.FromArgb(45, 45, 45);
                form.ForeColor = Color.White;

                var label = new Label()
                {
                    Text = "Enter Claude API Key:",
                    Location = new Point(10, 10),
                    Size = new Size(200, 20),
                    ForeColor = Color.White
                };

                var textBox = new TextBox()
                {
                    Location = new Point(10, 35),
                    Size = new Size(360, 20),
                    UseSystemPasswordChar = true,
                    BackColor = Color.FromArgb(60, 60, 60),
                    ForeColor = Color.White
                };

                var button = new Button()
                {
                    Text = "Save",
                    Location = new Point(10, 65),
                    Size = new Size(75, 23),
                    BackColor = Color.FromArgb(70, 130, 180),
                    ForeColor = Color.White,
                    FlatStyle = FlatStyle.Flat
                };

                button.Click += (s, e) => { form.DialogResult = DialogResult.OK; form.Close(); };
                form.AcceptButton = button;
                form.Controls.AddRange(new Control[] { label, textBox, button });

                if (form.ShowDialog() == DialogResult.OK)
                {
                    SaveApiKey(textBox.Text);
                    return textBox.Text;
                }
            }
            return "";
        }

        private void SaveApiKey(string apiKey)
        {
            string keyFile = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                                        "SystemConfig", "auth.dat");
            Directory.CreateDirectory(Path.GetDirectoryName(keyFile));
            File.WriteAllText(keyFile, EncryptString(apiKey));
        }

        private string EncryptString(string text)
        {
            return Convert.ToBase64String(Encoding.UTF8.GetBytes(text));
        }

        private string DecryptString(string encryptedText)
        {
            try
            {
                return Encoding.UTF8.GetString(Convert.FromBase64String(encryptedText));
            }
            catch
            {
                return "";
            }
        }

        private async Task<string> SendToClaudeAPI(string base64Image, string question)
        {
            try
            {
                var requestBody = new
                {
                    model = "claude-3-5-sonnet-20241022",
                    max_tokens = 1000,
                    messages = new[]
                    {
                        new
                        {
                            role = "user",
                            content = new object[]
                            {
                                new { type = "text", text = question },
                                new
                                {
                                    type = "image",
                                    source = new
                                    {
                                        type = "base64",
                                        media_type = "image/png",
                                        data = base64Image
                                    }
                                }
                            }
                        }
                    }
                };

                var json = JsonSerializer.Serialize(requestBody);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Add("x-api-key", _apiKey);
                _httpClient.DefaultRequestHeaders.Add("anthropic-version", "2023-06-01");

                var response = await _httpClient.PostAsync("https://api.anthropic.com/v1/messages", content);
                var responseString = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var jsonResponse = JsonSerializer.Deserialize<JsonElement>(responseString);
                    return jsonResponse.GetProperty("content")[0].GetProperty("text").GetString();
                }
                else
                {
                    return $"API Error: {responseString}";
                }
            }
            catch (Exception ex)
            {
                return $"Connection Error: {ex.Message}";
            }
        }

        private void CleanupMemory(object sender, EventArgs e)
        {
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();
        }

        protected override CreateParams CreateParams
        {
            get
            {
                CreateParams cp = base.CreateParams;
                cp.ExStyle |= WS_EX_LAYERED | WS_EX_TRANSPARENT | WS_EX_TOOLWINDOW | WS_EX_NOACTIVATE;
                return cp;
            }
        }

        // Windows API imports
        [DllImport("user32.dll")]
        private static extern int SetWindowLong(IntPtr hWnd, int nIndex, int dwNewLong);

        [DllImport("user32.dll")]
        private static extern int GetWindowLong(IntPtr hWnd, int nIndex);

        [DllImport("user32.dll")]
        private static extern bool SetLayeredWindowAttributes(IntPtr hwnd, uint crKey, byte bAlpha, uint dwFlags);

        private void InitializeComponent()
        {
            this.SuspendLayout();
            this.AutoScaleDimensions = new SizeF(6F, 13F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.BackColor = Color.Black;
            this.TransparencyKey = Color.Black;
            this.ClientSize = new Size(Screen.PrimaryScreen.Bounds.Width, Screen.PrimaryScreen.Bounds.Height);
            this.FormBorderStyle = FormBorderStyle.None;
            this.Name = "OverlayForm";
            this.Text = "Screen Overlay";
            this.TopMost = true;
            this.WindowState = FormWindowState.Maximized;
            this.ResumeLayout(false);
        }
    }

    static class Program
    {
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            // Check if already running
            bool createdNew;
            using (var mutex = new Mutex(true, "StealthScreenAnalyzer_Singleton", out createdNew))
            {
                if (createdNew)
                {
                    Application.Run(new OverlayForm());
                }
                else
                {
                    Environment.Exit(0);
                }
            }
        }
    }
}
